<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require '../db.php'; // Include the database connection

if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php'); // Redirect to login if not logged in
    exit();
}

$user_id = $_SESSION['user_id'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <h1>Admin Dashboard</h1>
    </header>
    <nav>
        <div class="logo" style="display: inline-block;"><img src="../spendify.jpg"  width="50" height="50">Expense Tracker</div>
        <ul>
            <li><a href="../personal/personal_dashboard.php">Personal Expense</a></li>
            <li><a href="../group/group_dashboard.php">Group Expense</a></li>
            <li><a href="../aboutus.php">About Us</a></li>
            <li><a href="../contact.php">Contact Us</a></li>
        </ul>
    </nav>
    <aside>
    <ul type=none>
        <li></li><li><a href="admin_dashboard.php">Admin Dashboard</li>
       <li><a href="manage_user.php">Manage Users</a></li>
            <li><a href="manage_group.php">Manage Groups</a></li>
            <li><a href="system_status.php">System Status</a></li>
            <li><a href="logs.php">Logs</a></li><hr>
            <li><a href="../logout.php">Logout</a></li>
    </ul>
        </div>
    </div>
    </ul>
</aside>
</body>
</html>