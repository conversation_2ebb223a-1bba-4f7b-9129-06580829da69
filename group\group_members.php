<?php
session_start();
require '../db.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: ../login.php");
    exit();
}

// Process form submission for expense sharing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_SESSION['user_id'])) {
    $group_id = $_POST['group_id'];
    $name = trim($_POST['name']);
    $note = trim($_POST['note'] ?? '');
    $amount = floatval($_POST['amount']);
    $paid_by = $_SESSION['user_id'];
    $members_to_split = $_POST['members'] ?? [];

    // Basic validation
    if (empty($group_id) || empty($name) || $amount <= 0 || empty($members_to_split)) {
        die("Invalid input. Please fill all required fields.");
    }

    // Insert the group expense
    $stmt = $pdo->prepare("INSERT INTO group_expenses (group_id, name, amount, paid_by) VALUES (?, ?, ?, ?)");
    $stmt->execute([$group_id, $name, $amount, $paid_by]);
    $expense_id = $pdo->lastInsertId();

    // Ensure payer is not considered due
    $members_to_split = array_map('intval', $members_to_split);
    $members_to_split = array_unique($members_to_split);
    $memberCount = count($members_to_split);

    if ($memberCount === 0) {
        die("No valid members selected for splitting the expense.");
    }

    $share = round($amount / $memberCount, 2);

    // Insert shares (skip payer)
    foreach ($members_to_split as $user_id) {
        if ($user_id != $paid_by) {
            $stmtShare = $pdo->prepare("INSERT INTO expense_shares (expense_id, user_id, share_amount) VALUES (?, ?, ?)");
            $stmtShare->execute([$expense_id, $user_id, $share]);
        }
    }

    header("Location: group_dashboard.php?expense_added=1");
    exit();
} else {
    // Display group members page
    $user_id = $_SESSION['user_id'];
    
    // Fetch user's groups
    $stmt = $pdo->prepare("SELECT g.group_id, g.group_name 
                          FROM groups g
                          JOIN group_members gm ON g.group_id = gm.group_id
                          WHERE gm.user_id = ?");
    $stmt->execute([$user_id]);
    $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get group ID from URL if provided
    $selected_group_id = isset($_GET['group_id']) ? intval($_GET['group_id']) : null;
    $members = [];
    
    // If a group is selected, fetch its members
    if ($selected_group_id) {
        $memberStmt = $pdo->prepare("SELECT u.id, u.name, u.email 
                                    FROM users u
                                    JOIN group_members gm ON u.id = gm.user_id
                                    WHERE gm.group_id = ?");
        $memberStmt->execute([$selected_group_id]);
        $members = $memberStmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Group Members</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <?php include("nav.php"); ?>
    <main class="container" align="center">
        <section>
    <div class="card">
        <h2>View Group Members</h2>
        
        <form method="GET" action="group_members.php">
            <label for="group_id">Select Group:</label>
            <select name="group_id" id="group_id" onchange="this.form.submit()">
                <option value="" disabled <?php echo !$selected_group_id ? 'selected' : ''; ?>>Select a group</option>
                <?php foreach ($groups as $group): ?>
                    <option value="<?= $group['group_id'] ?>" <?php echo $selected_group_id == $group['group_id'] ? 'selected' : ''; ?>>
                        <?= htmlspecialchars($group['group_name']) ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </form>
        
        <?php if ($selected_group_id && !empty($members)): ?>
            <h3>Members</h3>
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($members as $member): ?>
                        <tr>
                            <td><?= htmlspecialchars($member['name']) ?></td>
                            <td><?= htmlspecialchars($member['email']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php elseif ($selected_group_id): ?>
            <p>No members found for this group.</p>
        <?php else: ?><br><br>
            <p>Please select a group to view its members.</p>
        <?php endif; ?>
    </div>
    </section>
</main>
    <footer>
    <p>&copy; 2025 Expense Tracker. All Rights Reserved.</p>
    </footer>
</body>
</html>
