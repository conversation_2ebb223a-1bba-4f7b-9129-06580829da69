
<?php
session_start();
include '../db.php'; // Include the database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header('Location: ../login.php'); // Redirect to login if not logged in
    exit();
}

$user_id = $_SESSION['user_id'];

// Retrieve expense for the logged-in user
$stmt = $pdo->prepare("SELECT * FROM expenses WHERE user_id = ?");
$stmt->execute([$user_id]);
$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <script src="scripts.js"></script>
</head>
<body>
<?php include 'navbar.php'; ?>
<main class="container" align="center">
    <section>
        <div class="card" >
        <div id="expenseModal" class="modal">
  <div class="modal-content">
    <!-- <span class="close" id="closeModal">&times;</span> -->
    <h2>Add Expense</h2>
    <form id="addExpenseForm" action="add_expense.php" method="POST">
      <label for="name">Expense Name:</label>
      <input type="text" id="name" name="name" required><br><br>
      
      <label for="amount">Amount:</label>
      <input type="number" id="amount" name="amount" step="0.01" required><br><br>

      <label for="datetime">Date:</label>
      <input type="datetime-local" id="datetime" name="datetime" required><br><br>
      
      <button type="submit" class=btn>Add Expense</button>
    </form>
  </div>
</div>
        <h2>Your Expenses</h2>
        <div id="expense-list" class="expense-list">
            <?php foreach ($expenses as $expense): ?>
                <div class="expense-item">
                    <span><?php echo htmlspecialchars($expense['name']); ?>: ₹<?php echo htmlspecialchars(number_format($expense['amount'], 2)); ?></span>
                    <div>
                        <a href="edit_expense.php?id=<?php echo $expense['id']; ?>" class="btn-edit">Edit</a>
                        <a href="delete_expense.php?id=<?php echo $expense['id']; ?>" class="btn-delete">Delete</a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        </div>
    </section>
</main>

<script>
// Set default date and time to current India time
document.addEventListener('DOMContentLoaded', function() {
    const datetimeInput = document.getElementById('datetime');
    if (!datetimeInput.value) {
        const now = new Date();
        const indiaTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Kolkata"}));
        
        const year = indiaTime.getFullYear();
        const month = String(indiaTime.getMonth() + 1).padStart(2, '0');
        const day = String(indiaTime.getDate()).padStart(2, '0');
        const hours = String(indiaTime.getHours()).padStart(2, '0');
        const minutes = String(indiaTime.getMinutes()).padStart(2, '0');
        
        const formattedDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;
        datetimeInput.value = formattedDateTime;
    }
});
</script>

</body>
<?php include "../footer.php"; ?>
</html>
