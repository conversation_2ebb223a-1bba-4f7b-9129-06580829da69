/* Base Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', sans-serif;
    background: white;
    color: #ffffff;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Header */
header {
    padding: 20px;
    text-align: center;
    background: linear-gradient(135deg, #2c3e50, #4ca1af);
    font-size: 1.8rem;
    font-weight: bold;
    /* box-shadow: 0 2px 6px rgba(0,0,0,0.5); */
    width: 100%;
    position: fixed;
    top:0;
    z-index: 1000;
}

main.container {
    flex: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background:whitesmoke;
    border-radius: 10px;
    margin: 80px auto;
    width: 800px;
    height: 800px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.5);
    content: normal; 
    position: relative;
    color:black;
    top: 150px;
}
/* Navigation Bar */
nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    padding: 15px 30px;
    /* box-shadow: 0 2px 6px rgba(0,0,0,0.4); */
    position: fixed;
    top: 90px; /* Adjust based on header height */
    width: 100%;
    z-index: 1000;
}

nav .logo {
    font-size: 1.5rem;
    font-weight: bold;
    width: auto;
    height: auto;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 25px;
    position: relative;
}

nav ul li a {
    color: #fff;
    text-decoration: none;
    padding: 8px 14px;
    transition: background 0.3s ease;
    border-radius: 6px;
}

nav ul li a:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

/* Sidebar */
aside {
    background: linear-gradient(135deg, #2c3e50, #4ca1af);
    padding: 20px;
    width: 250px;
    height: 300vh; /* Account for header + nav height */
    position: fixed;
    top: 150px;
    /* border-right: 2px solid rgba(255, 255, 255, 0.1); */
    overflow-y: auto;
} 

aside h2 {
    font-size: 1.4rem;
    margin-bottom: 20px;
    color: #f5f5f5;
}

aside ul {
    list-style-type: none;
}

aside ul li {
    margin-bottom: 15px;
}

aside ul li a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

aside ul li a:hover {
    color: #00adb5;
}

/* Buttons */
.btn {
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-radius: 5px;
    margin-top: 1rem;
}

.btn:hover {
    background-color: rgba(0, 0, 0, 0.25);
}

.btn-cancel {
    background-color: #3498db;
    text-decoration: none;
    padding: 0.5rem 1rem;
    color: white;
    border-radius: 5px;
    display: inline-block;
    margin-top: 1rem;
}

.btn-cancel:hover {
    background-color: #2980b9;
}

/* Expense List */
.expense-list {
    margin-top: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    color: #333;
}

.expense-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #ddd;
}

.expense-item:last-child {
    border-bottom: none;
}

.btn-delete, .btn-edit {
    color: white;
    text-decoration: none;
    padding: 0.3rem 0.6rem;
    border-radius: 3px;
}

.btn-delete {
    background-color: #e74c3c;
}

.btn-edit {
    background-color: #3498db;
}

.btn-delete:hover {
    background-color: #c0392b;
}

.btn-edit:hover {
    background-color: #2980b9;
}

/* Footer */
footer {
    text-align: center;
    padding: 0.5rem;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
    width: 100%;
    position: absolute;
    bottom: 0;
}
    .avatar { width: 40px; height: 40px; background: #3498db; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 18px; margin-right: 10px; }
    .member { display: flex; align-items: center; margin-bottom: 10px; color:black }
    table { width: 100%; border-collapse: collapse; margin-top: 20px; background-Color:white; color:black; }
    th, td { border: 1px solid black; padding: 10px; text-align: center; }
    th { background-color:white; }
    .paid { color: green; }
    .due { color: red; }
    .group-link { margin-bottom: 10px; }

.card1 {
    background-color:transparent;
    /* color: black; */
    padding: 20px 40px;
    border-radius: 5px;
    text-decoration: none;
    display:flex;
    margin-top: 10px;

}
.card1:hover {
    background-color: #f0f0f0;
}
.card2 {
    background-color: transparent;
    padding: 1rem 5rem;
    border-radius: 5px;
    text-decoration: none;
    display: inline-block;
    margin-top: 10px;
}
.card2:hover {
    background-color: #f0f0f0;
}

.card3 {
    background-color: transparent;
    color: black;
    padding: 2rem;
    border-radius: 5px;
    text-decoration: none;
    display: flex;
    justify-content: space-between;
    /* width: 60%; */
}
.card3:hover {
    background-color: #f0f0f0;
    transition: background-color 0.3s ease;
}