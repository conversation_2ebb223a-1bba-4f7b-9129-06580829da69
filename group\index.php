<?php
session_start();
include '../db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header('Location: ../login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// Get user's groups
$stmt = $pdo->prepare("SELECT g.group_id, g.group_name FROM groups g
    JOIN group_members gm ON g.group_id = gm.group_id
    WHERE gm.user_id = ?");
$stmt->execute([$user_id]);
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get group ID from URL
$group_id = $_GET['group_id'] ?? null;
$group_name = 'Unknown Group';

if ($group_id) {
    // Get group name for selected group
    $stmt = $pdo->prepare("SELECT group_name FROM groups WHERE group_id = ?");
    $stmt->execute([$group_id]);
    $group = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($group) {
        $group_name = $group['group_name'];
    } else {
        echo "Group not found.";
        exit();
    }
} else {
    echo "No group selected.";
    exit();
}

// Get members of the group
$memberStmt = $pdo->prepare("SELECT u.id, u.name FROM users u
    JOIN group_members gm ON u.id = gm.user_id
    WHERE gm.group_id = ?");
$memberStmt->execute([$group_id]);
$members = $memberStmt->fetchAll(PDO::FETCH_ASSOC);

// Get dues and paid status
$balanceStmt = $pdo->prepare("SELECT
    u.id,
    u.name,
    (SELECT COALESCE(SUM(es.share_amount), 0)
     FROM expense_shares es
     JOIN group_expenses ge ON es.expense_id = ge.id
     WHERE es.user_id = u.id AND ge.group_id = ?) AS due,
    (SELECT COALESCE(SUM(ge.amount), 0)
     FROM group_expenses ge
     WHERE ge.paid_by = u.id AND ge.group_id = ?) AS paid
FROM users u
JOIN group_members gm ON u.id = gm.user_id
WHERE gm.group_id = ?
GROUP BY u.id, u.name");
$balanceStmt->execute([$group_id, $group_id, $group_id]);
$balances = $balanceStmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Group Overview</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <?php include('nav.php');?>
    <main class="container" align="center">
        <div class="card1">
    <div class="card2">
        <h2>Group: <?php echo htmlspecialchars($group_name); ?></h2>
        <h3>Total Members: <?php echo count($members); ?></h3>

        <div>
            <?php foreach ($members as $member): ?>
                <div class="member">
                    <div class="avatar"><?php echo strtoupper($member['name'][0]); ?></div>
                    <span><?php echo htmlspecialchars($member['name']); ?></span>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <div class="card2">
        <h3>Contribution Pie Chart</h3>
        <canvas id="contributionChart" width="100" height="100"></canvas>
    </div>
</div>
         <div class="card3">

        <h3 >Expense Summary</h3>

        <table>
            <caption>Members' Contribution</caption>
            <tr>
                <th>Name</th>
                <th>Paid</th>
                <th>Due</th>
            </tr>
            <?php foreach ($balances as $row): ?>
            <tr>
                <td><?php echo htmlspecialchars($row['name']); ?></td>
                <td class="paid"><?php echo number_format($row['paid'] ?? 0, 2); ?></td>
                <td class="due"><?php echo number_format(($row['due'] ?? 0) - ($row['paid'] ?? 0), 2); ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
        </div>
    </main>
    <script>


    const memberNames = <?php echo json_encode(array_column($balances, 'name')); ?>;
    const memberPaid = <?php echo json_encode(array_map(function($row) {
        return round($row['paid'] ?? 0, 2);
    }, $balances)); ?>;



    const ctx = document.getElementById('contributionChart').getContext('2d');
    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: memberNames,
            datasets: [{
                label: 'Contribution Amount',
                data: memberPaid,
                backgroundColor: [
                    '#3498db', '#2ecc71', '#e74c3c', '#f1c40f',
                    '#9b59b6', '#1abc9c', '#e67e22', '#34495e',
                    '#95a5a6', '#c0392b', '#16a085', '#2980b9'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const name = context.label;
                            const value = context.parsed;
                            return `${name}: ₹${value}`;
                        }
                    }
                }
            }
        }
    });
</script>
<footer>
    <p>&copy; 2025 Expense Tracker. All Rights Reserved.</p>
</footer>
</body>
</html>
