<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include ('../db.php'); // Include your database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header("Location: ../login.php");
    exit();
}

// Fetch user's groups
$user_id = $_SESSION['user_id'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Group Expense Tracker Dashboard</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <h2>Group Expense Tracker</h2>
    </header>
    <nav>
        <div class="logo" style="display: inline-block;"><img src="../spendify.jpg" alt="Expense Tracker"width="50" height="50"></div>
        <ul >
            <li><a href="../personal/personal_dashboard.php">Personal Expense</a></li>
            <li><a href="group_dashboard.php">Group Expenses</a></li>
            <li><a href="../aboutus.php">About Us</a></li>
            <li><a href="../contact.php">Contact Us</a></li>
            <li><a href="../logout.php" class="logout-btn">Logout</a></li>
        </UL>
    </nav>
    <aside>
    <ul>
        <li><a href="create_group.php">Create Group</a></li>
        <li><a href="my_groups.php">My Groups</a></li>
        <li><a href="add_expenses.php">Add Expense</a></li>
        <li><a href="join_group.php">Join Group</a></li><hr>
        <li><a href="../logout.php">Logout</a></li>

</ul>
</aside>
</body>
</html>