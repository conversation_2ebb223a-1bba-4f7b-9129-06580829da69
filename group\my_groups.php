<?php
session_start();
require '../db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header("Location: ../login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Fetch user's groups with additional information
$stmt = $pdo->prepare("
    SELECT g.group_id, g.group_name, g.created_at, g.join_link,
           COUNT(DISTINCT gm.user_id) as member_count,
           COALESCE(SUM(ge.amount), 0) as total_expenses
    FROM groups g 
    INNER JOIN group_members gm ON g.group_id = gm.group_id 
    LEFT JOIN group_expenses ge ON g.group_id = ge.group_id
    WHERE gm.user_id = ?
    GROUP BY g.group_id, g.group_name, g.created_at, g.join_link
    ORDER BY g.created_at DESC
");
$stmt->execute([$user_id]);
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch user's name
$stmt = $pdo->prepare("SELECT name FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);
$user_name = $user['name'];

// Get selected group details if group_id is provided
$selected_group = null;
$group_members = [];
$group_balances = [];
$group_expenses = [];

if (isset($_GET['group_id'])) {
    $selected_group_id = intval($_GET['group_id']);
    
    // Get group details
    $stmt = $pdo->prepare("SELECT * FROM groups WHERE group_id = ?");
    $stmt->execute([$selected_group_id]);
    $selected_group = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($selected_group) {
        // Get group members
        $memberStmt = $pdo->prepare("
            SELECT u.id, u.name, u.email 
            FROM users u
            JOIN group_members gm ON u.id = gm.user_id
            WHERE gm.group_id = ?
        ");
        $memberStmt->execute([$selected_group_id]);
        $group_members = $memberStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get balances (dues and paid)
        $balanceStmt = $pdo->prepare("
            SELECT
                u.id,
                u.name,
                (SELECT COALESCE(SUM(es.share_amount), 0)
                 FROM expense_shares es
                 JOIN group_expenses ge ON es.expense_id = ge.id
                 WHERE es.user_id = u.id AND ge.group_id = ?) AS due,
                (SELECT COALESCE(SUM(ge.amount), 0)
                 FROM group_expenses ge
                 WHERE ge.paid_by = u.id AND ge.group_id = ?) AS paid
            FROM users u
            JOIN group_members gm ON u.id = gm.user_id
            WHERE gm.group_id = ?
            GROUP BY u.id, u.name
        ");
        $balanceStmt->execute([$selected_group_id, $selected_group_id, $selected_group_id]);
        $group_balances = $balanceStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get recent group expenses
        $expenseStmt = $pdo->prepare("
            SELECT ge.*, u.name as paid_by_name
            FROM group_expenses ge
            JOIN users u ON ge.paid_by = u.id
            WHERE ge.group_id = ?
            ORDER BY ge.date DESC
            LIMIT 10
        ");
        $expenseStmt->execute([$selected_group_id]);
        $group_expenses = $expenseStmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Groups - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .groups-container {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .groups-list {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .group-item {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .group-item:hover {
            border-color: #4ca1af;
            background: #e8f4f8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 161, 175, 0.2);
        }
        
        .group-item.active {
            border-color: #2c3e50;
            background: linear-gradient(135deg, #2c3e50, #4ca1af);
            color: white;
        }
        
        .group-details {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .group-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #2c3e50, #4ca1af);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .members-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .member-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4ca1af;
        }
        
        .balance-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .balance-table th,
        .balance-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .balance-table th {
            background: linear-gradient(135deg, #2c3e50, #4ca1af);
            color: white;
            font-weight: 600;
        }
        
        .balance-table tr:hover {
            background: #f8f9fa;
        }
        
        .paid { color: #27ae60; font-weight: bold; }
        .due { color: #e74c3c; font-weight: bold; }
        
        .no-group-selected {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }
        
        .no-group-selected i {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        @media (max-width: 768px) {
            .groups-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <?php include("nav.php"); ?>
    <main class="container">
        <h1 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">
            Welcome, <?= htmlspecialchars($user_name) ?> - My Groups
        </h1>
        
        <div class="groups-container">
            <!-- Groups List -->
            <div class="groups-list">
                <h2 style="color: #2c3e50; margin-bottom: 20px;">Your Groups (<?= count($groups) ?>)</h2>
                
                <?php if (count($groups) > 0): ?>
                    <?php foreach ($groups as $group): ?>
                        <div class="group-item <?= (isset($_GET['group_id']) && $_GET['group_id'] == $group['group_id']) ? 'active' : '' ?>" 
                             onclick="window.location.href='my_groups.php?group_id=<?= $group['group_id'] ?>'">
                            <h3 style="margin: 0 0 10px 0; color: inherit;">
                                <?= htmlspecialchars($group['group_name']) ?>
                            </h3>
                            <div style="display: flex; justify-content: space-between; font-size: 14px; opacity: 0.8;">
                                <span><?= $group['member_count'] ?> members</span>
                                <span>₹<?= number_format($group['total_expenses'], 2) ?></span>
                            </div>
                            <div style="font-size: 12px; margin-top: 8px; opacity: 0.7;">
                                Created: <?= date('M d, Y', strtotime($group['created_at'])) ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div style="text-align: center; padding: 40px 20px; color: #7f8c8d;">
                        <p>You haven't joined any groups yet.</p>
                        <a href="create_group.php" style="color: #4ca1af; text-decoration: none;">Create your first group</a>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Group Details -->
            <div class="group-details">
                <?php if ($selected_group): ?>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2 style="color: #2c3e50; margin: 0;">
                            <?= htmlspecialchars($selected_group['group_name']) ?>
                        </h2>
                        <div style="font-size: 14px; color: #7f8c8d;">
                            Join Code: <strong><?= htmlspecialchars($selected_group['join_link']) ?></strong>
                        </div>
                    </div>
                    
                    <!-- Group Statistics -->
                    <div class="group-stats">
                        <div class="stat-card">
                            <div class="stat-value"><?= count($group_members) ?></div>
                            <div class="stat-label">Total Members</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">₹<?= number_format(array_sum(array_column($group_balances, 'paid')), 2) ?></div>
                            <div class="stat-label">Total Paid</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">₹<?= number_format(array_sum(array_column($group_balances, 'due')), 2) ?></div>
                            <div class="stat-label">Total Due</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value"><?= count($group_expenses) ?></div>
                            <div class="stat-label">Recent Expenses</div>
                        </div>
                    </div>
                    
                    <!-- Members Section -->
                    <h3 style="color: #2c3e50; margin: 30px 0 15px 0;">Group Members</h3>
                    <div class="members-grid">
                        <?php foreach ($group_members as $member): ?>
                            <div class="member-card">
                                <div style="display: flex; align-items: center;">
                                    <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #2c3e50, #4ca1af); 
                                                color: white; border-radius: 50%; display: flex; align-items: center; 
                                                justify-content: center; font-weight: bold; margin-right: 12px;">
                                        <?= strtoupper($member['name'][0]) ?>
                                    </div>
                                    <div>
                                        <div style="font-weight: 600; color: #2c3e50;">
                                            <?= htmlspecialchars($member['name']) ?>
                                        </div>
                                        <div style="font-size: 12px; color: #7f8c8d;">
                                            <?= htmlspecialchars($member['email']) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Balance Summary -->
                    <?php if (!empty($group_balances)): ?>
                        <h3 style="color: #2c3e50; margin: 30px 0 15px 0;">Balance Summary</h3>
                        <table class="balance-table">
                            <thead>
                                <tr>
                                    <th>Member</th>
                                    <th>Amount Paid</th>
                                    <th>Amount Due</th>
                                    <th>Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($group_balances as $balance): ?>
                                    <?php $net_balance = ($balance['paid'] ?? 0) - ($balance['due'] ?? 0); ?>
                                    <tr>
                                        <td><?= htmlspecialchars($balance['name']) ?></td>
                                        <td class="paid">₹<?= number_format($balance['paid'] ?? 0, 2) ?></td>
                                        <td class="due">₹<?= number_format($balance['due'] ?? 0, 2) ?></td>
                                        <td class="<?= $net_balance >= 0 ? 'paid' : 'due' ?>">
                                            ₹<?= number_format(abs($net_balance), 2) ?>
                                            <?= $net_balance >= 0 ? ' (Credit)' : ' (Owes)' ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                    
                    <!-- Recent Expenses -->
                    <?php if (!empty($group_expenses)): ?>
                        <h3 style="color: #2c3e50; margin: 30px 0 15px 0;">Recent Expenses</h3>
                        <div style="background: #f8f9fa; border-radius: 8px; padding: 15px;">
                            <?php foreach (array_slice($group_expenses, 0, 5) as $expense): ?>
                                <div style="display: flex; justify-content: space-between; align-items: center; 
                                           padding: 10px 0; border-bottom: 1px solid #e0e0e0;">
                                    <div>
                                        <div style="font-weight: 600; color: #2c3e50;">
                                            <?= htmlspecialchars($expense['name']) ?>
                                        </div>
                                        <div style="font-size: 12px; color: #7f8c8d;">
                                            Paid by <?= htmlspecialchars($expense['paid_by_name']) ?> • 
                                            <?= date('M d, Y', strtotime($expense['date'])) ?>
                                        </div>
                                    </div>
                                    <div style="font-weight: bold; color: #e74c3c;">
                                        ₹<?= number_format($expense['amount'], 2) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <div class="no-group-selected">
                        <div style="font-size: 48px; margin-bottom: 20px;">👥</div>
                        <h3>Select a Group</h3>
                        <p>Choose a group from the left to view its details, members, and expenses.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
    
    <?php include "../footer.php"; ?>
</body>
</html>
