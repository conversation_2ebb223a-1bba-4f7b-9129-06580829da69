<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include '../db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: ../login.php');
    exit();
}

// Example: Get database size or server info
$server_info = php_uname();
$db_size_query = "SELECT table_name, data_length, index_length FROM information_schema.tables WHERE table_schema = DATABASE()";
$stmt = $pdo->query($db_size_query);
$tables = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html>
<head>
    <title>System Status</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
<?php include 'bar.php' ?>
<main class="container" align="center">
<h2>System Status</h2>
<h3>Server Info</h3>
<pre><?= htmlspecialchars($server_info) ?></pre>

<h3>Database Tables Size</h3>
<table border="1">
<tr><th>Table Name</th><th>Data Length (Bytes)</th><th>Index Length (Bytes)</th></tr>
<?php foreach ($tables as $table): ?>
<tr>
    <td><?= $table['table_name'] ?></td>
    <td><?= $table['data_length'] ?></td>
    <td><?= $table['index_length'] ?></td>
</tr>
<?php endforeach; ?>
</table>

<a href="admin_dashboard.php">Back to Dashboard</a>
</main>
</body>
<?php include '../footer.php' ?>
</html>