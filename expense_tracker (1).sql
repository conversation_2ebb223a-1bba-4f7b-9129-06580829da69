-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 21, 2025 at 11:01 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `expense_tracker`
--

-- --------------------------------------------------------

--
-- Table structure for table `expenses`
--

CREATE TABLE `expenses` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `expenses`
--

INSERT INTO `expenses` (`id`, `user_id`, `name`, `amount`, `created_at`, `date`) VALUES
(1, 1, 'food', 200.00, '2025-05-06 06:07:11', '2025-05-06 08:07:11'),
(2, 1, 'bottle', 20.00, '2025-05-06 06:51:59', '2025-05-06 08:51:59'),
(3, 6, 'food', 350.00, '2025-05-07 06:56:00', '2025-05-07 08:56:00'),
(4, 6, 'Travel', 50.00, '2025-05-07 06:57:02', '2025-05-07 08:57:02'),
(5, 3, 'food', 200.00, '2025-05-11 15:05:24', '2025-05-11 17:05:24'),
(6, 6, 'bottle', 20.00, '2025-05-12 07:09:38', '2025-05-12 09:09:38'),
(7, 6, 'food', 40.00, '2025-05-19 15:16:26', '2025-05-19 17:16:26');

-- --------------------------------------------------------

--
-- Table structure for table `expense_shares`
--

CREATE TABLE `expense_shares` (
  `id` int(11) NOT NULL,
  `expense_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `share_amount` decimal(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `expense_shares`
--

INSERT INTO `expense_shares` (`id`, `expense_id`, `user_id`, `share_amount`) VALUES
(2, 2, 2, 200.00),
(3, 2, 6, 200.00),
(4, 3, 1, 150.00),
(5, 3, 2, 150.00),
(6, 4, 3, 50.00),
(7, 4, 4, 50.00),
(8, 5, 4, 250.00),
(9, 5, 6, 250.00);

-- --------------------------------------------------------

--
-- Table structure for table `groups`
--

CREATE TABLE `groups` (
  `group_id` int(11) NOT NULL,
  `group_name` varchar(255) NOT NULL,
  `join_link` varchar(255) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `groups`
--

INSERT INTO `groups` (`group_id`, `group_name`, `join_link`, `created_by`, `created_at`) VALUES
(1, 'Manali', '288024ae', 1, '2025-05-06 06:07:23'),
(2, 'College', '3b8a5835', 3, '2025-05-06 07:22:35'),
(3, 'GTS', '8008b35a', 3, '2025-05-11 15:08:14'),
(4, 'friends', 'zzz', 5, '2025-05-19 15:25:27');

-- --------------------------------------------------------

--
-- Table structure for table `group_expenses`
--

CREATE TABLE `group_expenses` (
  `id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `note` text NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `date` datetime NOT NULL,
  `paid_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `group_expenses`
--

INSERT INTO `group_expenses` (`id`, `group_id`, `name`, `note`, `amount`, `date`, `paid_by`, `created_at`) VALUES
(2, 1, 'Travel', '', 400.00, '0000-00-00 00:00:00', 1, '2025-05-07 06:29:20'),
(3, 1, 'FOOD', '', 300.00, '0000-00-00 00:00:00', 6, '2025-05-07 06:31:03'),
(4, 2, 'Travel', '', 100.00, '0000-00-00 00:00:00', 6, '2025-05-07 06:34:52'),
(5, 2, 'Travel', '', 500.00, '0000-00-00 00:00:00', 3, '2025-05-11 15:06:21');

-- --------------------------------------------------------

--
-- Table structure for table `group_members`
--

CREATE TABLE `group_members` (
  `id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `joined_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `group_members`
--

INSERT INTO `group_members` (`id`, `group_id`, `user_id`, `joined_at`) VALUES
(1, 1, 1, '2025-05-06 06:07:23'),
(2, 1, 2, '2025-05-06 06:08:22'),
(3, 2, 3, '2025-05-06 07:22:35'),
(4, 2, 4, '2025-05-06 07:23:38'),
(5, 2, 6, '2025-05-06 07:26:08'),
(6, 1, 6, '2025-05-06 07:27:02'),
(7, 3, 3, '2025-05-11 15:08:14'),
(8, 3, 7, '2025-05-11 17:14:52'),
(9, 3, 6, '2025-05-12 07:11:55'),
(10, 4, 8, '2025-05-19 15:26:15');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('user','admin') DEFAULT 'user',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `password`, `role`, `created_at`) VALUES
(1, 'DIVYA', '<EMAIL>', '$2y$10$RKpuCG5fzAiaZiqkBEo/8e5VQahnjB39VrpGLn5pGt1xg28/1yXOi', 'user', '2025-05-06 05:35:17'),
(2, 'NIKITA', '<EMAIL>', '$2y$10$/qMO0HJfG9S4AstCWwiIe.w..zwufb9xskRcx3OydplJT/FbaUkY.', 'user', '2025-05-06 06:08:15'),
(3, 'GUNJAN', '<EMAIL>', '$2y$10$H6/Y/KUO1Gnux5yEK.6WpenULxsDEJ8sKlufNs4SyB6HRhdlm8vxK', 'user', '2025-05-06 07:22:26'),
(4, 'SANIYA', '<EMAIL>', '$2y$10$emH.COXoKiIiStp3yIsaxuLQ8PeP64fMaLxx1AnZCP48ZvwJw3sDC', 'user', '2025-05-06 07:23:30'),
(5, 'MAHAK', '<EMAIL>', '$2y$10$EWLG1pf0z0szMlRt.d5Lk.WfwAOj8m0JYklotLT1NpYlf/SX8XOVy', 'admin', '2025-05-06 07:25:18'),
(6, 'SALONI', '<EMAIL>', '$2y$10$gTD.zBgJkXQ1N5vzz5/XDu2PIWgy2P1kMJn.bPtbXWdg37nmIVBOC', 'user', '2025-05-06 07:26:01'),
(7, 'TIYA', '<EMAIL>', '$2y$10$frR3k5rlKuUSQ44atVznVOnJx.gZiUU6cYuAl.BCHcJUyTGXkOcBG', 'user', '2025-05-11 15:09:01'),
(8, 'RANJEET', '<EMAIL>', '$2y$10$S3hqRm/X0EC8ATIJfvyRnedkvrfZorzk9PT4i38CdDgQS6Ort96N2', 'user', '2025-05-19 15:23:52');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `expenses`
--
ALTER TABLE `expenses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `expense_shares`
--
ALTER TABLE `expense_shares`
  ADD PRIMARY KEY (`id`),
  ADD KEY `expense_id` (`expense_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `groups`
--
ALTER TABLE `groups`
  ADD PRIMARY KEY (`group_id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `group_expenses`
--
ALTER TABLE `group_expenses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `group_id` (`group_id`),
  ADD KEY `paid_by` (`paid_by`);

--
-- Indexes for table `group_members`
--
ALTER TABLE `group_members`
  ADD PRIMARY KEY (`id`),
  ADD KEY `group_id` (`group_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `expenses`
--
ALTER TABLE `expenses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `expense_shares`
--
ALTER TABLE `expense_shares`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `groups`
--
ALTER TABLE `groups`
  MODIFY `group_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `group_expenses`
--
ALTER TABLE `group_expenses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `group_members`
--
ALTER TABLE `group_members`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `expenses`
--
ALTER TABLE `expenses`
  ADD CONSTRAINT `expenses_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `expense_shares`
--
ALTER TABLE `expense_shares`
  ADD CONSTRAINT `expense_shares_ibfk_1` FOREIGN KEY (`expense_id`) REFERENCES `group_expenses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `expense_shares_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `groups`
--
ALTER TABLE `groups`
  ADD CONSTRAINT `groups_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `group_expenses`
--
ALTER TABLE `group_expenses`
  ADD CONSTRAINT `group_expenses_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `groups` (`group_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `group_expenses_ibfk_2` FOREIGN KEY (`paid_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `group_members`
--
ALTER TABLE `group_members`
  ADD CONSTRAINT `group_members_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `groups` (`group_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `group_members_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
