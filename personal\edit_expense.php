<?php
session_start();
require '../db.php'; // Include the database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header('Location: ../login.php'); // Redirect to login if not logged in
    exit();
}

if (isset($_GET['id'])) {
    $expense_id = $_GET['id'];

    // Fetch the expense details
    $stmt = $pdo->prepare("SELECT * FROM expenses WHERE id = ? AND user_id = ?");
    $stmt->execute([$expense_id, $_SESSION['user_id']]);
    $expense = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$expense) {
        echo "Expense not found.";
        exit();
    }
} else {
    echo "Invalid request.";
    exit();
}

// Handle form submission for updating
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = trim($_POST['expense-name']);
    $amount = floatval($_POST['expense-amount']);

    if (empty($name) || $amount <= 0) {
        $error = "Please provide a valid expense name and amount.";
    } else {
        // Prepare SQL for updating
        $updateStmt = $pdo->prepare("UPDATE expenses SET name = ?, amount = ? WHERE id = ?");
        if ($updateStmt->execute([$name, $amount, $expense_id])) {
            header('Location: personal_dashboard.php'); // Redirect back to dashboard
            exit();
        } else {
            $error = "Failed to update expense.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Expense - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
<header>
<h1>Welcome to Your Dashboard</h1>
<? include 'navbar.php'; ?>
<main class="container" align="center">
    <?php if (isset($error)): ?>
        <p class="error"><?php echo htmlspecialchars($error); ?></p>
    <?php endif; ?>
    <form action="" method="POST">
        <div class="form-group">
            <label for="expense-name">Expense Name:</label>
            <input type="text" id="expense-name" name="expense-name" value="<?php echo htmlspecialchars($expense['name']); ?>" required>
        </div>
        <div class="form-group">
            <label for="expense-amount">Expense Amount:</label>
            <input type="number" id="expense-amount" name="expense-amount" value="<?php echo htmlspecialchars($expense['amount']); ?>" step="0.01" required>
        </div>
        <button type="submit" class="btn">Update Expense</button>
        <a href="personal_dashboard.php" class="btn-cancel">Cancel</a>
    </form>
</main>
</body>
</html>