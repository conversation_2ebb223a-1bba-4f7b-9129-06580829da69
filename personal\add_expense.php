<?php
session_start();
require '../db.php'; // Your database connection, assume PDO

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header('Location: ../login.php');
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_id = $_SESSION['user_id'];
    $name = trim($_POST['name']);
    $amount = floatval($_POST['amount']);
    $date = date('Y-m-d H:i:s'); // Current date and time

    // Basic validation
    if ($name != '' && $amount > 0) {
        $stmt = $pdo->prepare("INSERT INTO expenses (user_id, name, amount ,date) VALUES (?, ?, ?,?)");
        $stmt->execute([$user_id, $name, $amount, $date]);
        header('Location: addexpense.php' . $_SERVER['http://localhost/project/personal/addexpense.php']); // back to dashboard
        exit();
    } else {
        // handle validation error if needed
        die('Invalid input.');
    }
}
?>