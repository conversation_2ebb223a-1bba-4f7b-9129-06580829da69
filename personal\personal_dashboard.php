<?php
session_start();
require '../db.php'; // Include the database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header('Location: ../login.php'); // Redirect to login if not logged in
    exit();
}

$user_id = $_SESSION['user_id'];

// Retrieve expenses for the logged-in user
$stmt = $pdo->prepare("SELECT * FROM expenses WHERE user_id = ?");
$stmt->execute([$user_id]);
$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch the user's name from the database
$stmt = $pdo->prepare("SELECT name FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);
$user_name = $user['name']; // Store the user's name

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
<?php include 'navbar.php'; ?>
<main class="container" align="center">
                <h2>Welcome, <?= htmlspecialchars($user_name) ?></h2> <!-- Display the user's name -->

        <h2>Your Expenses</h2>
        <!-- <button id="add-expense-btn" class="btn">Add Expense</button> -->
        <div id="expense-list" class="expense-list">
            <?php foreach ($expenses as $expense): ?>
                <div class="expense-item">
                    <span><?php echo htmlspecialchars($expense['name']); ?>: ₹<?php echo htmlspecialchars(number_format($expense['amount'], 2)); ?></span>
                    <div>
                        <a href="edit_expense.php?id=<?php echo $expense['id']; ?>" class="btn-edit">Edit</a>
                        <a href="delete_expense.php?id=<?php echo $expense['id']; ?>" class="btn-delete">Delete</a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
</main>
</body>
</html>