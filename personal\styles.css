/* Base Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', sans-serif;
    background: white;  
    color: #ffffff;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position:relative;
    top:0;
}

/* Header */
header {
    padding: 20px;
    text-align: center;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    font-size: 1.8rem;
    font-weight: bold;
    /* box-shadow: 0 2px 6px rgba(0,0,0,0.5); */
    width: 100%;
    position: fixed;
    top:0;
    z-index: 1000;
}

main.container {
    flex: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 20px;
    background-color:whitesmoke;
    border-radius: 10px;
    margin-top: 170px;
    margin-bottom: 20px;
    margin-left: 250px; /* Account for sidebar width */
    margin-right: 0;
    width: calc(100vw - 250px - 40px); /* Full remaining width minus padding */
    max-width: 900px; /* Maximum width for better readability */
    /* Center the container in the remaining space */
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    height: auto;
    box-shadow: 0 2px 6px rgba(0,0,0,0.5);
    color:black;
    min-height: calc(100vh - 200px);
    text-align: center;
}

/* Navigation Bar */
nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    padding: 15px 30px;
    position: fixed;
    top: 90px;
    /* box-shadow: 0 2px 6px rgba(0,0,0,0.4); */
    width: 100%;
    z-index: 1000;
}

nav .logo {
    font-size: 1.5rem;
    font-weight: bold;
    width: auto;
    height: auto;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 25px;
}

nav ul li a {
    color: #fff;
    text-decoration: none;
    padding: 8px 14px;
    /* transition: background 0.3s ease; */
    border-radius: 6px;
}

nav ul li a:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

/* Logout Button Styling */
nav ul li a.logout-btn {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-weight: 600;
    transition: all 0.3s ease;
}

nav ul li a.logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

/* Sidebar */
aside {
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    padding: 20px;
    width: 250px;
    height: calc(100vh - 150px); /* Account for header + nav height */
    position: fixed;
    top: 150px;
    left: 0;
    /* border-right: 2px solid rgba(255, 255, 255, 0.1); */
    overflow-y: auto;
    z-index: 999;
}

aside h2 {
    font-size: 1.4rem;
    margin-bottom: 20px;
    color: #f5f5f5;
}

aside ul {
    list-style-type: none;
}

aside ul li {
    margin-bottom: 15px;
}

aside ul li a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    padding: 12px 15px;
    border-radius: 8px;
    display: block;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

aside ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s ease;
}

aside ul li a:hover {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateX(10px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

aside ul li a:hover::before {
    left: 100%;
}

/* Sidebar Logout Button Styling */
aside ul li a[href*="logout"] {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: block;
    text-align: center;
    font-weight: 600;
    margin-top: 10px;
    transition: all 0.3s ease;
}

aside ul li a[href*="logout"]:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    color: #ffffff;
    transform: translateY(-1px);
}

/* For responsive layout */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
        gap: 10px;
    }

    aside {
        width: 100%;
        position: relative;
        min-height: auto;
        border-right: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        margin-top: 20px;
        top: 0;
        height: auto;
    }

    main.container {
        margin: 170px 20px 20px 20px; /* Remove left margin for mobile */
        width: calc(100% - 40px);
        text-align: center;
    }

    aside ul li a {
        transform: none; /* Disable transform on mobile */
    }

    aside ul li a:hover {
        transform: none;
    }
}
.btn {
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-radius: 5px;
    margin-top: 1rem;
}
.btn:hover {
    background-color: rgba(0, 0, 0, 0.25);
}
.btn-cancel {
    background-color: #3498db;
    text-decoration: none;
    padding: 0.5rem 1rem;
    color: white;
    border-radius: 5px;
    display: inline-block;
    margin-top: 1rem;
}
.btn-cancel:hover {
    background-color: #2980b9;
}
.expense-list {
    margin-top: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    color: #333;
    width: 700px;
    height: auto;
}
.expense-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #ddd;
    color: #333;
}
.expense-item:last-child {
    border-bottom: none;
}
.btn-delete, .btn-edit {
    color: white;
    text-decoration: none;
    padding: 0.3rem 0.6rem;
    border-radius: 3px;
}
.btn-delete {
    background-color: #e74c3c;
}
.btn-edit {
    background-color: #3498db;
}
.btn-delete:hover {
    background-color: #c0392b;
}
.btn-edit:hover {
    background-color: #2980b9;
}
canvas {
    margin-top: 2rem;
    background: white;
    padding: 1rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
/* Footer */
footer {
    text-align: center;
    padding: 1rem;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
    margin-top: 2rem;
    width: 100%;
    position: absolute;
    bottom: 0;
}
.error {
    color: red;
    margin-bottom: 1rem;
}

/* Modal and Form Styling */
.modal {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    margin: 20px 0;
}

.modal-content {
    text-align: center;
}

.modal-content h2 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.modal-content form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-width: 400px;
    margin: 0 auto;
}

.modal-content label {
    text-align: left;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.modal-content input {
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.modal-content input:focus {
    outline: none;
    border-color: #4ca1af;
    box-shadow: 0 0 0 3px rgba(76, 161, 175, 0.1);
}

/* Card styling for better organization */
.card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin: 20px 0;
}