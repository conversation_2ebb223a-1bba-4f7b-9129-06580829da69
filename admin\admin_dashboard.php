<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include '../db.php'; // Include your database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: ../login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// Fetch the user's name from the database
$stmt = $pdo->prepare("SELECT name FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    echo "User not found.";
    exit();
}

$user_name = $user['name']; // Store the user's name
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
<?php include 'bar.php'  ?>
        <main class="container" align="center">
            <h2>Welcome, <?= htmlspecialchars($user_name) ?></h2> <!-- Display the user's name -->
            <p>Select options from the menu to manage your expenses.</p>
            <div class="card">

        </main>
</body>
<?php include '../footer.php' ?>
</html>
