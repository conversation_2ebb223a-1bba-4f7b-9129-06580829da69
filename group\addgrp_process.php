<?php
session_start();
require '../db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_SESSION['user_id'])) {
    $group_id = $_POST['group_id'];
    $name = trim($_POST['name']);
    $note = $_POST['note'] ?? '';
    $amount = floatval($_POST['amount']);
    $paid_by = $_SESSION['user_id'];

    // Basic validation
    if (empty($group_id) || empty($name) || $amount <= 0) {
        die("Invalid input. Please fill all required fields.");
    }

    // Insert the group expense
    $stmt = $pdo->prepare("INSERT INTO group_expenses (group_id, name, amount, paid_by) VALUES (?, ?, ?, ?)");
    $stmt->execute([$group_id, $name, $amount, $paid_by]);
    $expense_id = $pdo->lastInsertId();

    // Get all group members except the payer
    $memberStmt = $pdo->prepare("SELECT user_id FROM group_members WHERE group_id = ? AND user_id != ?");
    $memberStmt->execute([$group_id, $paid_by]);
    $members = $memberStmt->fetchAll(PDO::FETCH_COLUMN);

    $memberCount = count($members);
    if ($memberCount === 0) {
        // If there are no other members, no need to split
        header("Location: group_dashboard.php?expense_added=1");
        exit();
    }

    // Calculate equal share for each member
    $share = round($amount / $memberCount, 2);

    // Insert shares for all group members except the payer
    foreach ($members as $user_id) {
        $stmtShare = $pdo->prepare("INSERT INTO expense_shares (expense_id, user_id, share_amount) VALUES (?, ?, ?)");
        $stmtShare->execute([$expense_id, $user_id, $share]);
    }

    header("Location: group_dashboard.php?expense_added=1");
    exit();
}

// Handle AJAX request to fetch group members (keeping this for reference)
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['group_id'])) {
    $group_id = intval($_GET['group_id']);
    $stmt = $pdo->prepare("SELECT u.id AS user_id, u.name FROM users u
                           JOIN group_members gm ON u.id = gm.user_id
                           WHERE gm.group_id = ?");
    $stmt->execute([$group_id]);
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    header('Content-Type: application/json');
    echo json_encode($members);
    exit();
}

echo "Invalid request.";
