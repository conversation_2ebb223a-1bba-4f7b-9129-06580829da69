<?php
session_start();
include ('../db.php'); // Include your database connection
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header("Location: ../login.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Group</title>
    <link rel="stylesheet" href="styles.css"> <!-- Link to your custom CSS -->
</head>
<body>
<?php include('nav.php');?>
<main class="container" align="center">
        <form action="create_group_action.php" method="POST" >
            <div class="card">
                <h1>Create Group</h1>
                <label for="group_name">Group Name:</label>
                <input type="text" name="group_name" id="group_name" required>
                <button type="submit" class="btn">Create Group</button>
            </div>               
        </form>
          <!-- Display Invite Link if Available -->
        <?php if (isset($_SESSION['invite_link'])): ?>
            <div class="card">
                Invite Link: 
                <input type="text" id="copyinvitelink" value="<?= $_SESSION['invite_link'] ?>" copyable readonly>
                <button class="copy-btn" onclick="copyInviteLink()">Copy Link</button>
            </div>
            <?php unset($_SESSION['invite_link']); // Clear the invite link after displaying ?>
        <?php endif; ?>

        </div>
        <a href="group_dashboard.php">Back to Dashboard</a>
</main>

<footer>
    <p>&copy; 2025 Expense Tracker. All Rights Reserved.</p>
</footer>

</body>
</html>