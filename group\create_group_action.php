<?php
session_start();
require '../db.php'; // Include your database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header("Location: ../login.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $group_name = trim($_POST['group_name']);
    $user_id = $_SESSION['user_id'];

    if (empty($group_name)) {
        echo "<script>alert('Group name cannot be empty.'); window.location.href = 'create_group.php';</script>";
        exit();
    }
}

    // Generate a unique group code
    $join_link = bin2hex(random_bytes(4)); // Generates an 8-character unique code

    // Insert the group into the database
    $stmt = $pdo->prepare("INSERT INTO groups (group_name, created_by, join_link) VALUES (?, ?, ?)");
    if ($stmt->execute([$group_name,$user_id, $join_link,])) {
        $group_id = $pdo->lastInsertId();

        // Add the creator as a member of the group
        $memberStmt = $pdo->prepare("INSERT INTO group_members (group_id, user_id) VALUES (?, ?)");
        $memberStmt->execute([$group_id, $user_id]);

        // Store the invite link in the session
        $_SESSION['invite_link'] = "http://localhost/project/group/join_group.php?code=" . $join_link;

        // Redirect back to the create group page with success
        echo "<script>alert('Group created successfully!'); window.location.href = 'create_group.php';</script>";
    } else {
        echo "<script>alert('Failed to create group. Please try again.'); window.location.href = 'create_group.php';</script>";
    }
?>