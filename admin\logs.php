<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include '../db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: ../login.php');
    exit();
}

// Example: Fetch logs from a logs table if exists
// For now, static logs
$logs = [
    ['timestamp' => '2024-01-01 12:00:00', 'action' => 'User admin logged in'],
    ['timestamp' => '2024-01-02 09:30:00', 'action' => 'Added new user <PERSON>'],
];

?>

<!DOCTYPE html>
<html>
<head>
    <title>Logs</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <?php include 'bar.php' ?>
    <main class="container" align="center">
<h2>System Logs</h2>
<table border="1">
<tr><th>Timestamp</th><th>Action</th></tr>
<?php foreach ($logs as $log): ?>
<tr>
    <td><?= $log['timestamp'] ?></td>
    <td><?= htmlspecialchars($log['action']) ?></td>
</tr>
<?php endforeach; ?>
</table>

<a href="admin_dashboard.php">Back to Dashboard</a>
</main>
</body>
<?php include '../footer.php' ?>
</html>