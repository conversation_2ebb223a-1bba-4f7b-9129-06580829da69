<?php
session_start();
require '../db.php'; // Include the database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header('Location: login.php'); // Redirect to login if not logged in
    exit();
}

if (isset($_GET['id'])) {
    $expense_id = $_GET['id'];

    // Prepare SQL statement
    $stmt = $pdo->prepare("DELETE FROM expenses WHERE id = ? AND user_id = ?");
    
    if ($stmt->execute([$expense_id, $_SESSION['user_id']])) {
        header('Location: personal_dashboard.php'); // Redirect back to dashboard
        exit();
    } else {
        echo "Failed to delete expense.";
    }
}
?>