document.addEventListener('DOMContentLoaded', function () {
    // const registrationForm = document.getElementById('registration-form');
    // const loginForm = document.getElementById('login-form');
    const addExpenseBtn = document.getElementById('add-expense-btn');
    const addExpenseModal = document.getElementById('add-expense-modal');
    const closeModalBtn = document.querySelector('.close-btn');
    const addExpenseForm = document.getElementById('add-expense-form');
    const expenseList = document.getElementById('expense-list');

    // Handle registration form submission
    // if (registrationForm) {
    //     registrationForm.addEventListener('submit', function (e) {
    //         // Remove the preventDefault() to allow form submission
    //         // Form will submit directly to register.php
    //     });
    // }

    // // Handle login form submission
    // if (loginForm) {
    //     loginForm.addEventListener('submit', function (e) {
    //         // Remove the preventDefault() to allow form submission
    //         // Form will submit directly to login.php
    //     });
    // }
//
    addExpenseForm.addEventListener('submit', function(e) {
        e.preventDefault();
    
        const formData = new FormData(addExpenseForm);
    
        fetch('add_expense.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            console.log(data); // Show server response (for debugging)
            // Refresh the expense list or redirect to dashboard
        })
        .catch(error => {
            console.error('Error:', error);
        });
    });
    //
    // Handle adding new expense
    addExpenseForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const expenseName = document.getElementById('expense-name').value;
        const expenseAmount = document.getElementById('expense-amount').value;

        // Create a new expense item
        const expenseItem = document.createElement('div');
        expenseItem.className = 'expense-item';
        expenseItem.innerText = `${expenseName}: ₹${expenseAmount}`; 
        
        // Add the new expense to the expense list
        expenseList.appendChild(expenseItem);

        // Clear the form inputs
        addExpenseForm.reset();
        
        // Close the modal
        addExpenseModal.style.display = "none";

        console.log("Expense added:", expenseName, expenseAmount); // You can handle this later
    });
});

// creating a group
document.getElementById('createGroupForm').addEventListener('submit', function(event) {
    event.preventDefault();

    const groupName = document.getElementById('group_name').value;

    fetch('create_group_action.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ group_name: groupName, user_id: 1 }) // Assuming user_id = 1 for demo purposes
    })
    .then(response => response.json())
    .then(data => {
        alert(data.message);
    })
    .catch((error) => {
        console.error('Error:', error);
    });
});
function copyInviteLink() {
    const inviteLink = document.getElementById('invite_link');
    inviteLink.select();
    inviteLink.setSelectionRange(0, 99999); // For mobile devices
    navigator.clipboard.writeText(inviteLink.value)
        .then(() => alert('Invite link copied to clipboard!'))
        .catch(err => alert('Failed to copy invite link.'));
}