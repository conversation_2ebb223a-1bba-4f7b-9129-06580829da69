<?php
session_start();
include 'db.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    $password = $_POST['password'];

    try {
        $stmt = $pdo->prepare("SELECT id, password, role FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['role'] = $user['role'];
            if ($user['role'] === 'admin') {
                header('Location: /project/admin/admin_dashboard.php');
                } else {
                    header('Location: /project/personal/personal_dashboard.php');
                }
            exit();
        } else {
            echo "<script>alert('Invalid email or password!');</script>";
        }
    } catch(PDOException $e) {
        echo "<script>alert('<PERSON><PERSON> failed. Please try again.');</script>";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <script src="scripts.js" defer></script>
</head>
<body>
    <header>
        <h2>Login to Your Account</h2>
        <p>Welcome back to Expense Tracker</p>
    </header>

    <main class="container">
        <form id="login-form" class="form-container" method="post" action="login.php">
            <div class="form-group">
                <input type="email" id="email" name="email" placeholder="Email" required>
            </div>
            <div class="form-group">
                <input type="password" id="password" name="password" placeholder="Password" required>
            </div>
            <button type="submit" class="btn">Login</button>
            <p class="text-center">Don't have an account? <a href="register.php">Register here</a></p>
        </form>
    </main>
</body>

    <footer>
        <p>&copy; 2025 Expense Tracker. All Rights Reserved.</p>
    </footer>
</html>