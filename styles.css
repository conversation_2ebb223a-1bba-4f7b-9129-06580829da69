/* styles.css */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: white;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    position: relative;
}

/* Header */
header {
    padding: 20px;
    text-align: center;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    font-size: 1.8rem;
    font-weight: bold;
    /* box-shadow: 0 2px 6px rgba(0,0,0,0.5); */
    width: 100%;
    position: fixed;
    top: 0;
}

main.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: transparent;
    border-radius: 10px;
    margin: 20px 50px;
    width: 100%;
    /* box-shadow: 0 2px 6px rgba(0,0,0,0.5); */
    z-index: 1000;
}
/* Navigation Bar */
nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    padding: 15px 30px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.4);
    width: 100%;

}

nav .logo {
    font-size: 1.5rem;
    font-weight: bold;
    width: auto;
    height: auto;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 25px;
}

nav ul li a {
    color: #fff;
    text-decoration: none;
    padding: 8px 14px;
    transition: background 0.3s ease;
    border-radius: 6px;
}

nav ul li a:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

.form-container {
    background: transparent;
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    width: 500px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    margin-top: 5px;
    border: none;
    border-radius: 8px;
    background:whitesmoke;
    color: linear-gradient(135deg, #2c3e50, #4ca1af);
    font-size: 1rem;
}

.form-group input::placeholder {
    color: linear-gradient(135deg, #2c3e50, #4ca1af);
}

.form-group select {
    color: linear-gradient(135deg, #2c3e50, #4ca1af);
    appearance: auto;
}

.form-group select option {
    background: whitesmoke;
    color: linear-gradient(135deg, #2c3e50, #4ca1af) ;
}
.form-group select option:hover {
    background: #f0f0f0;
}

/* Button */
.btn {
    width: 100%;
    padding: 0.9rem;
    border: none;
    border-radius: 8px;
    background: #2c3e50;
    color: #fff;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.btn:hover {
    transform: scale(1.03);
}
.text-center {
    text-align: center;
    color: black;
}

.text-center a {
    color: gray;
    text-decoration: none;
}

.text-center a:hover {
    text-decoration: underline;
}

/* Footer */
footer {
    text-align: center;
    padding: 1rem;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
    margin-top: 2rem;
    position: absolute;
    width: 100%;
    bottom: 0;
    height: auto;
}