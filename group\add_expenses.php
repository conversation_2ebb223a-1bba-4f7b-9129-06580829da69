<?php
session_start();
require '../db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header("Location: ../login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Fetch groups where user is a member
$stmt = $pdo->prepare("SELECT g.group_id, g.group_name 
                       FROM groups g
                       JOIN group_members gm ON g.group_id = gm.group_id
                       WHERE gm.user_id = ?");
$stmt->execute([$user_id]);
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Add Group Expense</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <?php include("nav.php"); ?>
<main class="container" align="center">
<div class="card">
<div class="container">
    <h2>Add Group Expense</h2>
    <form action="addgrp_process.php" method="POST" id="expenseForm">
        <label for="group_id">Select Group:</label>
        <select name="group_id" id="group_id" required>
            <option value="" disabled selected>Select a group</option>
            <?php foreach ($groups as $group): ?>
                <option value="<?= $group['group_id'] ?>"><?= htmlspecialchars($group['group_name']) ?></option>
            <?php endforeach; ?>
        </select><br><br>

        <label for="name">Expense Name:</label>
        <input type="text" name="name" required><br><br>

        <label for="note">Note (optional):</label>
        <textarea name="note" rows="3"></textarea><br><br>

        <label for="amount">Total Amount:</label>
        <input type="number" name="amount" step="0.01" required><br><br>
        
        <button type="submit" class="btn">Add Expense</button>
    </form>
</div>
</main>
<?php include '../footer.php'; ?>
</body>
</html>
