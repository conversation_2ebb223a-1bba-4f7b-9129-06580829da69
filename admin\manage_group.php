<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include '../db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: ../login.php');
    exit();
}

// Handle add group
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $group_name = $_POST['group_name'];
    $join_link = $_POST['join_link'];
    $created_by = $_SESSION['user_id'];
    $stmt = $pdo->prepare("INSERT INTO groups (group_name, join_link, created_by) VALUES (?, ?, ?)");
    $stmt->execute([$group_name, $join_link, $created_by]);
    header('Location: manage_group.php');
}

// Handle delete group
if (isset($_GET['delete'])) {
    $id = (int)$_GET['delete'];
    $pdo->prepare("DELETE FROM groups WHERE group_id = ?")->execute([$id]);
    header('Location: maange_group.php');
}

// Fetch groups
$stmt = $pdo->query("SELECT * FROM groups");
$groups = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Manage Groups</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <?php include 'bar.php' ?>
    <main class="container" align="center">
<h2>Manage Groups</h2>

<form method="POST" action="">
    <h3>Add New Group</h3><BR>
    <label>Group Name:</label><input type="text" name="group_name" required><br><BR>
    <label>Join Link:</label><input type="text" name="join_link" required><br><BR>
    <button type="submit">Add Group</button>
</form>

<h3>Existing Groups</h3>
<table border="1">
<tr><th>ID</th><th>Name</th><th>Join Link</th><th>Actions</th></tr>
<?php foreach ($groups as $group): ?>
<tr>
    <td><?= $group['group_id'] ?></td>
    <td><?= htmlspecialchars($group['group_name']) ?></td>
    <td><?= htmlspecialchars($group['join_link']) ?></td>
    <td>
        <a href="?delete=<?= $group['group_id'] ?>" onclick="return confirm('Are you sure?')">Delete</a>
    </td>
</tr>
<?php endforeach; ?>
</table>

<!-- <a href="admin_dashboard.php">Back to Dashboard</a> -->
</main>
</body>
<?php include '../footer.php' ?>
</html>