<?php
session_start();
include ('../db.php'); // Include your database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header("Location: ../login.php");
    exit();
}

// Fetch user's groups
$user_id = $_SESSION['user_id'];
$stmt = $pdo->prepare("
    SELECT g.group_id, g.group_name 
    FROM groups g 
    INNER JOIN group_members gm ON g.group_id = gm.group_id 
    WHERE gm.user_id = ?
");
$stmt->execute([$user_id]);
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch the user's name from the database
$stmt = $pdo->prepare("SELECT name FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);
$user_name = $user['name']; // Store the user's name
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Group Expense Tracker Dashboard</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <?php include("nav.php"); ?>
<main class="container" align="center">
    <div class="card">
                    <h2>Welcome, <?= htmlspecialchars($user_name) ?></h2> <!-- Display the user's name -->
    <section>
        <div class="container">
            <h2>Your Groups</h2>
            <ul class="group-list">
                <?php if (count($groups) > 0): ?>
                    <?php foreach ($groups as $group): ?>
                        <li type=none>
                            <button style:color="white" width="50px" height="50px"><a href="index.php?group_id=<?= $group['group_id'] ?>">
                                <?= htmlspecialchars($group['group_name']) ?>
                            </a></button>
                        </li>
                    <?php endforeach; ?>
                <?php else: ?>
                    <li>No groups found. Create a new group!</li>
                <?php endif; ?>
            </ul>
        </div>
    </section>
    </div>
 </main>
<?php include '../footer.php'; ?>
</body>
</html>