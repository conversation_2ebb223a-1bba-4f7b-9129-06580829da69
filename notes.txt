        <?php if ($_SESSION['role'] == 'admin'): ?>
            <!-- <h3>Admin Panel Options</h3> -->
            <!-- Admin-specific options here (e.g., managing users) -->
        <?php endif; ?>
        this personal expense tracker for admin users.










        <?php
session_start();
require '../db.php'; // Include the database connection

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!isset($_SESSION['user_id'])) {
        echo "You must be logged in to add expenses.";
        exit();
    }

    $user_id = $_SESSION['user_id'];
    $name = $_POST['expense-name'];
    $amount = $_POST['expense-amount'];

    // Prepare SQL statement
    $stmt = $pdo->prepare("INSERT INTO expenses (user_id, name, amount) VALUES (?, ?, ?)");
    
    if ($stmt->execute([$user_id, $name, $amount])) {
        echo "Expense added successfully!";
    } else {
        echo "Failed to add expense.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
<header>
<h1>Welcome to Your Dashboard</h1>
</header>

<nav>
    <div class="">Expense Tracker</div>
    <ul>
        <li><a href="personal_dashboard.php">Personal Expense</a></li>
        <li><a href="group_dashboard.php">Group Expense</a></li>
    </ul>
    <div class="profile">
        <form action="../logout.php" method="POST" style="display: inline;">
            <button type="submit" class="logout-btn">Logout</button>
        </form>
    </div>
</nav>
<aside>
    <h2>Navigation</h2>
    <ul>
        <li><a href="personal_dashboard.php">Add Expense</a></li>
        <li><a href="view_expenses.php">View Expenses</a></li>
        <li><a href="reports.php">Reports</a></li>
    </ul>
</aside>
<main>
    <section>
        <h2>Your Expenses</h2>
        <button id="add-expense-btn" class="btn">Add Expense</button>
        <div id="expense-list" class="expense-list">
            <?php foreach ($expenses as $expense): ?>
                <div class="expense-item">
                    <span><?php echo htmlspecialchars($expense['name']); ?>: $<?php echo htmlspecialchars(number_format($expense['amount'], 2)); ?></span>
                    <div>
                        <a href="edit_expense.php?id=<?php echo $expense['id']; ?>" class="btn-edit">Edit</a>
                        <a href="delete_expense.php?id=<?php echo $expense['id']; ?>" class="btn-delete">Delete</a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </section>
</main>
<footer>
    <p>&copy; 2025 Expense Tracker. All Rights Reserved.</p>
</footer>
</body>
<script>
        const addExpenseBtn = document.getElementById('add-expense-btn');
        const addExpenseModal = document.getElementById('add-expense-modal');
        addExpenseForm.addEventListener('submit', function(e) {
        e.preventDefault();
    
        const formData = new FormData(addExpenseForm);
    
        fetch('add_expense.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            console.log(data); // Show server response (for debugging)
            // Refresh the expense list or redirect to dashboard
        })
        .catch(error => {
            console.error('Error:', error);
        });
    });
    //
    // Handle adding new expense
    addExpenseForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const expenseName = document.getElementById('expense-name').value;
        const expenseAmount = document.getElementById('expense-amount').value;

        // Create a new expense item
        const expenseItem = document.createElement('div');
        expenseItem.className = 'expense-item';
        expenseItem.innerText = `${expenseName}: ₹${expenseAmount}`; 
        
        // Add the new expense to the expense list
        expenseList.appendChild(expenseItem);

        // Clear the form inputs
        addExpenseForm.reset();
        
        // Close the modal
        addExpenseModal.style.display = "none";

        console.log("Expense added:", expenseName, expenseAmount); // You can handle this later
    });
});
</html>