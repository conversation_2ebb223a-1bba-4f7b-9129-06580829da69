* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    min-height: 100vh;
    background: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    position: relative;
}

/* Header */
header {
    padding: 20px;
    text-align: center;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    font-size: 1.8rem;
    font-weight: bold;
    /* box-shadow: 0 2px 6px rgba(0,0,0,0.5); */
    width: 100%;
    position: relative;
    top: 0;
}

/* Form Container with glass effect */
main.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: whitesmoke;
    border-radius: 10px;
    margin: 20px auto;
    max-width: 800px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.5);
}

/* Navigation Bar */
nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    padding: 15px 30px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.4);
    width: 100%;

}

nav .logo {
    font-size: 1.5rem;
    font-weight: bold;
    width: auto;
    height: auto;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 25px;
}

nav ul li a {
    color: #fff;
    text-decoration: none;
    padding: 8px 14px;
    transition: background 0.3s ease;
    border-radius: 6px;
}

nav ul li a:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

/* Button */
.btn {
    width: 100%;
    padding: 0.9rem;
    border: none;
    border-radius: 8px;
    background: #2c3e50;
    color: #fff;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.btn:hover {
    transform: scale(1.03);
}
.text-center {
    text-align: center;
}

.text-center a {
    color: #ddff00;
    text-decoration: none;
}

.text-center a:hover {
    text-decoration: underline;
}

/* Footer */
footer {
    text-align: center;
    padding: 0.75rem;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
    position: absolute;
    width: 100%;
    bottom: 0;
    display: inline;

}

    .aboutus {
    max-width: 900px;
    margin: 40px auto 20px auto;
    padding: 20px;
    background: whitesmoke;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    text-align: center;
}

.aboutus h2 {
    font-size: 28px;
    color: #4ca1af;
    margin-bottom: 10px;
}

.aboutus p {
    font-size: 16px;
    margin-top: 10px;
}

/* About Us Content */
.aboutus-content {
    max-width: 900px;
    margin: 0 auto 40px auto;
    padding: 20px;
    background: transparent;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    color: #2c3e50;
}

.aboutus-content h4 {
    color: #2c3e50;
    margin-top: 20px;
}

.aboutus-content p {
    margin-top: 10px;
    font-size: 15px;
}