<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require '../db.php'; // Include the database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header('Location: ../login.php'); // Redirect to login if not logged in
    exit();
}

$user_id = $_SESSION['user_id'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
<header>
<h2>Personal Expense Tracker</h2>
</header>
<nav>
        <div class="logo" style="display: inline-block;"><img src="../spendify.jpg"  width="50" height="50">Expense Tracker</div>
    <ul>
        <li><a href="personal_dashboard.php">Personal Expense</a></li>
        <li><a href="../group/group_dashboard.php">Group Expense</a></li>
        <li><a href="../aboutus.php">About Us</a></li>
        <li><a href="../contact.php">Contact Us</a></li>
        <li><a href="../logout.php" class="logout-btn">Logout</a></li>
    </ul>
</nav>
<main>
<aside>
    <ul>
        <li></li><li></li><li></li>
        <li><a href="addexpense.php">Add Expense</a></li>
        <li><a href="view_expenses.php">Total Expenses</a></li>
        <li><a href="reports.php">Reports</a></li>
        <li><a href="analytics.php">advanced analytics</a></li>
        <hr>
        <li><a href="../logout.php">Logout</a></li>
    </ul>
</aside>
</main>
</body>
</html>