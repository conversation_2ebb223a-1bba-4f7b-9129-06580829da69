<?php
session_start();
require '../db.php'; // Include the database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header('Location: login.php'); // Redirect to login if not logged in
    exit();
}

$user_id = $_SESSION['user_id'];

// Get filter parameters
$selected_month = isset($_GET['month']) ? $_GET['month'] : date('Y-m');
$selected_year = isset($_GET['year']) ? $_GET['year'] : date('Y');

// Retrieve expenses for the logged-in user
$stmt = $pdo->prepare("SELECT * FROM expenses WHERE user_id = ?");
$stmt->execute([$user_id]);
$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Monthly expenses query
$monthly_stmt = $pdo->prepare("SELECT name, amount, date, DATE_FORMAT(date, '%Y-%m-%d') as formatted_date
                               FROM expenses
                               WHERE user_id = ? AND DATE_FORMAT(date, '%Y-%m') = ?
                               ORDER BY date DESC");
$monthly_stmt->execute([$user_id, $selected_month]);
$monthly_expenses = $monthly_stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate monthly statistics
$monthly_total = array_sum(array_column($monthly_expenses, 'amount'));
$monthly_count = count($monthly_expenses);
$monthly_average = $monthly_count > 0 ? $monthly_total / $monthly_count : 0;

// Get monthly summary for the year
$yearly_stmt = $pdo->prepare("SELECT DATE_FORMAT(date, '%Y-%m') as month,
                              SUM(amount) as total_amount,
                              COUNT(*) as expense_count
                              FROM expenses
                              WHERE user_id = ? AND YEAR(date) = ?
                              GROUP BY DATE_FORMAT(date, '%Y-%m')
                              ORDER BY month");
$yearly_stmt->execute([$user_id, $selected_year]);
$yearly_summary = $yearly_stmt->fetchAll(PDO::FETCH_ASSOC);

// Prepare data for charts
$labels = [];
$data = [];
foreach ($yearly_summary as $month_data) {
    $labels[] = date('M Y', strtotime($month_data['month'] . '-01'));
    $data[] = $month_data['total_amount'];
}

// Daily expenses for current month
$daily_stmt = $pdo->prepare("SELECT DATE_FORMAT(date, '%Y-%m-%d') as day,
                             SUM(amount) as daily_total
                             FROM expenses
                             WHERE user_id = ? AND DATE_FORMAT(date, '%Y-%m') = ?
                             GROUP BY DATE_FORMAT(date, '%Y-%m-%d')
                             ORDER BY day");
$daily_stmt->execute([$user_id, $selected_month]);
$daily_expenses = $daily_stmt->fetchAll(PDO::FETCH_ASSOC);

$daily_labels = [];
$daily_data = [];
foreach ($daily_expenses as $day_data) {
    $daily_labels[] = date('d M', strtotime($day_data['day']));
    $daily_data[] = $day_data['daily_total'];
}

// Top expenses for pie chart
$pie_stmt = $pdo->prepare("SELECT name, SUM(amount) as total_amount
                           FROM expenses
                           WHERE user_id = ? AND DATE_FORMAT(date, '%Y-%m') = ?
                           GROUP BY name
                           ORDER BY total_amount DESC
                           LIMIT 10");
$pie_stmt->execute([$user_id, $selected_month]);
$pie_expenses = $pie_stmt->fetchAll(PDO::FETCH_ASSOC);

$pie_labels = [];
$pie_data = [];
foreach ($pie_expenses as $expense) {
    $pie_labels[] = $expense['name'];
    $pie_data[] = $expense['total_amount'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Reports - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .filter-section {
            margin: 20px 0;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 8px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }
        .chart-container {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .expense-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .expense-table th, .expense-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .expense-table th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
<?php include 'navbar.php'; ?>
<main class="container">
    <h1>Expense Reports & Analytics</h1>

    <!-- Filter Section -->
    <div class="filter-section">
        <form method="GET" style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
            <div>
                <label for="month">Select Month:</label>
                <input type="month" id="month" name="month" value="<?php echo $selected_month; ?>">
            </div>
            <div>
                <label for="year">Select Year:</label>
                <select id="year" name="year">
                    <?php for($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                        <option value="<?php echo $y; ?>" <?php echo ($y == $selected_year) ? 'selected' : ''; ?>>
                            <?php echo $y; ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            <button type="submit" class="btn">Filter</button>
        </form>
    </div>

    <!-- Monthly Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">₹<?php echo number_format($monthly_total, 2); ?></div>
            <div class="stat-label">Monthly Total</div>
        </div>
        <div class="stat-card">
            <div class="stat-value"><?php echo $monthly_count; ?></div>
            <div class="stat-label">Total Expenses</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">₹<?php echo number_format($monthly_average, 2); ?></div>
            <div class="stat-label">Average per Expense</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">₹<?php echo $monthly_count > 0 ? number_format($monthly_total / date('t', strtotime($selected_month . '-01')), 2) : '0.00'; ?></div>
            <div class="stat-label">Daily Average</div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="chart-container">
        <h3>Monthly Expenses Overview (<?php echo $selected_year; ?>)</h3>
        <canvas id="monthlyChart" width="400" height="200"></canvas>
    </div>

    <div class="chart-container">
        <h3>Daily Expenses for <?php echo date('F Y', strtotime($selected_month . '-01')); ?></h3>
        <canvas id="dailyChart" width="400" height="200"></canvas>
    </div>

    <div class="chart-container">
        <h3>Expense Distribution (Top 10)</h3>
        <canvas id="pieChart" width="400" height="200"></canvas>
    </div>

    <!-- Monthly Expenses Table -->
    <div class="chart-container">
        <h3>Expense Details for <?php echo date('F Y', strtotime($selected_month . '-01')); ?></h3>
        <?php if (count($monthly_expenses) > 0): ?>
            <table class="expense-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Expense Name</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($monthly_expenses as $expense): ?>
                        <tr>
                            <td><?php echo date('d M Y, H:i', strtotime($expense['date'])); ?></td>
                            <td><?php echo htmlspecialchars($expense['name']); ?></td>
                            <td>₹<?php echo number_format($expense['amount'], 2); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr style="font-weight: bold; background-color: #f8f9fa;">
                        <td colspan="2">Total</td>
                        <td>₹<?php echo number_format($monthly_total, 2); ?></td>
                    </tr>
                </tfoot>
            </table>
        <?php else: ?>
            <p>No expenses found for the selected month.</p>
        <?php endif; ?>
    </div>

</main>

<script>
// Monthly Chart
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
const monthlyChart = new Chart(monthlyCtx, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode($labels); ?>,
        datasets: [{
            label: 'Monthly Expenses',
            data: <?php echo json_encode($data); ?>,
            backgroundColor: 'rgba(54, 162, 235, 0.6)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '₹' + value.toLocaleString();
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return 'Amount: ₹' + context.parsed.y.toLocaleString();
                    }
                }
            }
        }
    }
});

// Daily Chart
const dailyCtx = document.getElementById('dailyChart').getContext('2d');
const dailyChart = new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($daily_labels); ?>,
        datasets: [{
            label: 'Daily Expenses',
            data: <?php echo json_encode($daily_data); ?>,
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '₹' + value.toLocaleString();
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return 'Amount: ₹' + context.parsed.y.toLocaleString();
                    }
                }
            }
        }
    }
});

// Pie Chart for Expense Distribution
const pieCtx = document.getElementById('pieChart').getContext('2d');
const pieChart = new Chart(pieCtx, {
    type: 'pie',
    data: {
        labels: <?php echo json_encode($pie_labels); ?>,
        datasets: [{
            data: <?php echo json_encode($pie_data); ?>,
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)',
                'rgba(255, 159, 64, 0.8)',
                'rgba(199, 199, 199, 0.8)',
                'rgba(83, 102, 255, 0.8)',
                'rgba(255, 99, 255, 0.8)',
                'rgba(99, 255, 132, 0.8)'
            ],
            borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 205, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)',
                'rgba(199, 199, 199, 1)',
                'rgba(83, 102, 255, 1)',
                'rgba(255, 99, 255, 1)',
                'rgba(99, 255, 132, 1)'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'right',
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                        return context.label + ': ₹' + context.parsed.toLocaleString() + ' (' + percentage + '%)';
                    }
                }
            }
        }
    }
});
</script>
</body>
<?php include "../footer.php" ?>
</html>