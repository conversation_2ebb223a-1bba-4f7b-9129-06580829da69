<?php
session_start();
require '../db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header('Location: ../login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// Get date range (default to last 6 months)
$end_date = date('Y-m-d');
$start_date = date('Y-m-d', strtotime('-6 months'));

if (isset($_GET['start_date']) && isset($_GET['end_date'])) {
    $start_date = $_GET['start_date'];
    $end_date = $_GET['end_date'];
}

// Spending trends analysis
$trends_stmt = $pdo->prepare("
    SELECT 
        DATE_FORMAT(date, '%Y-%m') as month,
        SUM(amount) as total,
        COUNT(*) as count,
        AVG(amount) as average
    FROM expenses 
    WHERE user_id = ? AND date BETWEEN ? AND ?
    GROUP BY DATE_FORMAT(date, '%Y-%m')
    ORDER BY month
");
$trends_stmt->execute([$user_id, $start_date, $end_date]);
$trends = $trends_stmt->fetchAll(PDO::FETCH_ASSOC);

// Day of week analysis
$dow_stmt = $pdo->prepare("
    SELECT 
        DAYNAME(date) as day_name,
        DAYOFWEEK(date) as day_num,
        SUM(amount) as total,
        COUNT(*) as count,
        AVG(amount) as average
    FROM expenses 
    WHERE user_id = ? AND date BETWEEN ? AND ?
    GROUP BY DAYOFWEEK(date), DAYNAME(date)
    ORDER BY day_num
");
$dow_stmt->execute([$user_id, $start_date, $end_date]);
$day_analysis = $dow_stmt->fetchAll(PDO::FETCH_ASSOC);

// Hour of day analysis
$hour_stmt = $pdo->prepare("
    SELECT 
        HOUR(date) as hour,
        SUM(amount) as total,
        COUNT(*) as count
    FROM expenses 
    WHERE user_id = ? AND date BETWEEN ? AND ?
    GROUP BY HOUR(date)
    ORDER BY hour
");
$hour_stmt->execute([$user_id, $start_date, $end_date]);
$hour_analysis = $hour_stmt->fetchAll(PDO::FETCH_ASSOC);

// Top expenses
$top_stmt = $pdo->prepare("
    SELECT name, amount, date
    FROM expenses 
    WHERE user_id = ? AND date BETWEEN ? AND ?
    ORDER BY amount DESC
    LIMIT 10
");
$top_stmt->execute([$user_id, $start_date, $end_date]);
$top_expenses = $top_stmt->fetchAll(PDO::FETCH_ASSOC);

// Expense frequency analysis
$freq_stmt = $pdo->prepare("
    SELECT 
        name,
        COUNT(*) as frequency,
        SUM(amount) as total_amount,
        AVG(amount) as avg_amount
    FROM expenses 
    WHERE user_id = ? AND date BETWEEN ? AND ?
    GROUP BY name
    HAVING frequency > 1
    ORDER BY frequency DESC
    LIMIT 10
");
$freq_stmt->execute([$user_id, $start_date, $end_date]);
$frequent_expenses = $freq_stmt->fetchAll(PDO::FETCH_ASSOC);

// Overall statistics
$stats_stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_expenses,
        SUM(amount) as total_amount,
        AVG(amount) as avg_expense,
        MIN(amount) as min_expense,
        MAX(amount) as max_expense,
        MIN(date) as first_expense_date,
        MAX(date) as last_expense_date
    FROM expenses 
    WHERE user_id = ? AND date BETWEEN ? AND ?
");
$stats_stmt->execute([$user_id, $start_date, $end_date]);
$overall_stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// Prepare chart data
$trend_labels = array_column($trends, 'month');
$trend_data = array_column($trends, 'total');
$trend_counts = array_column($trends, 'count');

$dow_labels = array_column($day_analysis, 'day_name');
$dow_data = array_column($day_analysis, 'total');

$hour_labels = array_column($hour_analysis, 'hour');
$hour_data = array_column($hour_analysis, 'total');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Analytics - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .analytics-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .filter-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        .chart-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-card h3 {
            margin-bottom: 20px;
            color: #2c3e50;
        }
        .table-card h3 {
            margin-bottom: 20px;
            color: #2c3e50;
        }
        .table-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .analytics-table {
            width: 100%;
            border-collapse: collapse;
        }
        .analytics-table th,
        .analytics-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
            color: #000;
        }
        .analytics-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #000;
        }
        .analytics-table tr:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
<?php include 'navbar.php'; ?>

<div class="analytics-container">
    <h1>Advanced Expense Analytics</h1>
    
    <!-- Date Filter -->
    <div class="filter-section">
        <form method="GET" style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
            <div>
                <label for="start_date">From:</label>
                <input type="date" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
            </div>
            <div>
                <label for="end_date">To:</label>
                <input type="date" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
            </div>
            <button type="submit" class="btn">Update Analysis</button>
        </form>
    </div>

    <!-- Overview Statistics -->
    <div class="stats-overview">
        <div class="stat-card">
            <div class="stat-value">₹<?php echo number_format($overall_stats['total_amount'] ?? 0, 2); ?></div>
            <div class="stat-label">Total Spent</div>
        </div>
        <div class="stat-card">
            <div class="stat-value"><?php echo $overall_stats['total_expenses'] ?? 0; ?></div>
            <div class="stat-label">Total Expenses</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">₹<?php echo number_format($overall_stats['avg_expense'] ?? 0, 2); ?></div>
            <div class="stat-label">Average Expense</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">₹<?php echo number_format($overall_stats['max_expense'] ?? 0, 2); ?></div>
            <div class="stat-label">Highest Expense</div>
        </div>
    </div>

    <!-- Charts -->
    <div class="chart-grid">
        <div class="chart-card">
            <h3>Spending Trends</h3>
            <canvas id="trendsChart"></canvas>
        </div>
        <div class="chart-card">
            <h3>Spending by Day of Week</h3>
            <canvas id="dayChart"></canvas>
        </div>
        <div class="chart-card">
            <h3>Spending by Hour</h3>
            <canvas id="hourChart"></canvas>
        </div>
        <div class="chart-card">
            <h3>Expense Frequency</h3>
            <canvas id="frequencyChart"></canvas>
        </div>
    </div>

    <!-- Analysis Tables -->
    <div class="table-card">
        <h3>Top 10 Highest Expenses</h3>
        <?php if (count($top_expenses) > 0): ?>
            <table class="analytics-table">
                <thead>
                    <tr>
                        <th>Expense Name</th>
                        <th>Amount</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($top_expenses as $expense): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($expense['name']); ?></td>
                            <td>₹<?php echo number_format($expense['amount'], 2); ?></td>
                            <td><?php echo date('d M Y, H:i', strtotime($expense['date'])); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p>No expenses found for the selected period.</p>
        <?php endif; ?>
    </div>

    
</div>

<script>
// Spending Trends Chart
const trendsCtx = document.getElementById('trendsChart').getContext('2d');
new Chart(trendsCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_map(function($month) { return date('M Y', strtotime($month . '-01')); }, $trend_labels)); ?>,
        datasets: [{
            label: 'Monthly Spending',
            data: <?php echo json_encode($trend_data); ?>,
            borderColor: 'rgba(54, 162, 235, 1)',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }, {
            label: 'Number of Expenses',
            data: <?php echo json_encode($trend_counts); ?>,
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            borderWidth: 2,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '₹' + value.toLocaleString();
                    }
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                beginAtZero: true,
                grid: {
                    drawOnChartArea: false,
                }
            }
        }
    }
});

// Day of Week Chart
const dayCtx = document.getElementById('dayChart').getContext('2d');
new Chart(dayCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode($dow_labels); ?>,
        datasets: [{
            data: <?php echo json_encode($dow_data); ?>,
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)',
                'rgba(255, 159, 64, 0.8)',
                'rgba(199, 199, 199, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': ₹' + context.parsed.toLocaleString();
                    }
                }
            }
        }
    }
});

// Hour Chart
const hourCtx = document.getElementById('hourChart').getContext('2d');
new Chart(hourCtx, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode(array_map(function($h) { return $h . ':00'; }, $hour_labels)); ?>,
        datasets: [{
            label: 'Spending by Hour',
            data: <?php echo json_encode($hour_data); ?>,
            backgroundColor: 'rgba(75, 192, 192, 0.6)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '₹' + value.toLocaleString();
                    }
                }
            }
        }
    }
});

// Frequency Chart
const freqCtx = document.getElementById('frequencyChart').getContext('2d');
new Chart(freqCtx, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode(array_column($frequent_expenses, 'name')); ?>,
        datasets: [{
            label: 'Frequency',
            data: <?php echo json_encode(array_column($frequent_expenses, 'frequency')); ?>,
            backgroundColor: 'rgba(153, 102, 255, 0.6)',
            borderColor: 'rgba(153, 102, 255, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        indexAxis: 'y',
        scales: {
            x: {
                beginAtZero: true
            }
        }
    }
});
</script>

</body>
<?php include "../footer.php" ?>
</html>
