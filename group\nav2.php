<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include ('../db.php'); // Include your database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header("Location: ../login.php");
    exit();
}

// Fetch user's groups
$user_id = $_SESSION['user_id'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Group Expense Tracker Dashboard</title>
    
    <style>
    /* Base Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
body {
    font-family: 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #1f4037, #99f2c8);
    color: #ffffff;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}
header {
    padding: 20px;
    text-align: center;
    background: rgba(0, 0, 0, 0.5);
    font-size: 2rem;
    font-weight: 700;
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
    width: 100%;
}
nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.35);
    padding: 15px 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.25);
}
nav .logo {
    font-size: 1.6rem;
    font-weight: 700;
}
nav ul {
    list-style: none;
    display: flex;
    gap: 30px;
}
nav ul li a {
    color: #fff;
    text-decoration: none;
    padding: 10px 16px;
    transition: all 0.3s ease;
    border-radius: 8px;
}
nav ul li a:hover {
    background: rgba(255, 255, 255, 0.2);
}
aside {
    background: rgba(0, 0, 0, 0.25);
    padding: 20px;
    width: 250px;
    height: 100vh;
    position: fixed;
    top: 140px;
    left: 0;
    border-right: 2px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
}
aside ul {
    list-style: none;
}
aside ul li {
    margin-bottom: 18px;
}
aside ul li a {
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 6px;
    display: block;
}
aside ul li a:hover {
    background: rgba(255, 255, 255, 0.15);
}
main.container {
    flex: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 25px;
    background: #f9f9f9;
    border-radius: 12px;
    margin: 20px auto;
    width: 850px;
    height: 800px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    color: #333;
}
.btn {
    background: linear-gradient(135deg, #ff6a00, #ee0979);
    color: #fff;
    border: none;
    padding: 0.6rem 1.2rem;
    cursor: pointer;
    border-radius: 6px;
    margin-top: 1rem;
    transition: all 0.3s ease;
}
.btn:hover {
    background: rgba(0, 0, 0, 0.25);
}
.btn-cancel {
    background: #3498db;
    padding: 0.6rem 1.2rem;
    color: #fff;
    border-radius: 6px;
    display: inline-block;
    margin-top: 1rem;
    text-decoration: none;
    transition: background 0.3s ease;
}
.btn-cancel:hover {
    background: #2980b9;
}
.expense-list {
    margin-top: 1rem;
    background: #fff;
    padding: 1.2rem;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    color: #333;
}
.expense-item {
    display: flex;
    justify-content: space-between;
    padding: 0.6rem 0;
    border-bottom: 1px solid #ddd;
}
.expense-item:last-child {
    border-bottom: none;
}
.btn-delete, .btn-edit {
    color: #fff;
    text-decoration: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}
.btn-delete {
    background: #e74c3c;
}
.btn-edit {
    background: #3498db;
}
.btn-delete:hover {
    background: #c0392b;
}
.btn-edit:hover {
    background: #2980b9;
}
footer {
    text-align: center;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.4);
    color: #fff;
    margin-top: 2rem;
    width: 100%;
    position: fixed;
    bottom: 0;
}
.avatar {
    width: 42px;
    height: 42px;
    background: #3498db;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 18px;
    margin-right: 12px;
}
.member {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: #333;
}
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background: #fff;
    color: #333;
}
th, td {
    border: 1px solid #ccc;
    padding: 12px;
    text-align: center;
}
th {
    background: #ecf0f1;
}
.paid {
    color: green;
}
.due {
    color: red;
}
.group-link {
    margin-bottom: 12px;
}
.card1, .card2, .card3 {
    background: rgba(255,255,255,0.85);
    padding: 18px 30px;
    border-radius: 10px;
    text-decoration: none;
    display: flex;
    margin-top: 14px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}
.card1:hover, .card2:hover, .card3:hover {
    background: #f0f0f0;
}
.card2 {
    padding: 1rem 3rem;
    display: inline-block;
}
.card3 {
    justify-content: space-between;
}

    </style>

</head>
<body>
    <header>
        <h2>Group Expense Tracker</h2>
    </header>
    <nav>
        <div class="logo" style="display: inline-block;"><img src="spendify.jpg" alt="Expense Tracker" width="50" height="50"></div>
        <ul>
            <li><a href="../PERSONAL/personal_dashboard.php">Personal Expense</a></li>
            <li><a href="group_dashboard.php">Group Expenses</a></li>
            <li><a href="../aboutus.php">About Us</a></li>
            <li><a href="../contact.php">Contact Us</a></li>
        </ul>
    </nav>
    <aside>
        <ul>
            <li><a href="create_group.php">Create Group</a></li>
            <li><a href="Group_dashboard.php">View Groups</a></li>
            <li><a href="add_expenses.php">Add Expense</a></li>
            <li><a href="http://localhost/project/group/index.php?group_id=1">Group Overview</a></li>
            <li><a href="group_members.php">View members</a></li>
            <li><a href="join_group.php">Join Group</a></li><hr>
            <li><a href="../logout.php">Logout</a></li>
        </ul>
    </aside>
</body>
</html>
