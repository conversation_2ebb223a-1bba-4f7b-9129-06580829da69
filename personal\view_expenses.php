<?php
session_start();
require '../db.php'; // Include the database connection

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php'); // Redirect to login if not logged in
    exit();
}

$user_id = $_SESSION['user_id'];

// Retrieve expenses for the logged-in user
$stmt = $pdo->prepare("SELECT * FROM expenses WHERE user_id = ?");
$stmt->execute([$user_id]);
$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetching expenses for charting
$stmt = $pdo->prepare("SELECT name, amount,date, DATE_FORMAT(created_at, '%Y-%m-%d-%d') as date FROM expenses WHERE user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$chartExpenses = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
<?php include 'navbar.php'; ?>
<main class="container" align="center">
<section>
        <h2>Your Expenses Report</h2>
        <div class="expense-list">
            <?php foreach ($expenses as $expense): ?>
                <div class="expense-item">
                    <span><?php echo htmlspecialchars($expense['name']); ?>: ₹<?php echo htmlspecialchars(number_format($expense['amount'], 2)); ?></span>
                </div>
            <?php endforeach; ?>
        </div>
        <b><right>Total Expenses: ₹<?php echo htmlspecialchars(number_format(array_sum(array_column($expenses, 'amount')), 2)); ?><right>
    </section>
</main>
</body>
<?php include("../footer.php"); ?>
</html>