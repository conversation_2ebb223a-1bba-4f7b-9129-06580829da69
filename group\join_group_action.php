<?php
session_start();
require '../db.php'; // Include your database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header("Location: ../login.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $join_link = trim($_POST['join_link']);
    $user_id = $_SESSION['user_id'];

    // Debugging: Log the submitted join_link
    error_log("Submitted join_link: " . $join_link);

    // Check if the group exists (case-insensitive comparison)
    $stmt = $pdo->prepare("SELECT group_id FROM groups WHERE LOWER(join_link) = LOWER(?)");
    $stmt->execute([$join_link]);
    $grp = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($grp) {
        $group_id = $grp['group_id'];

        // Check if the user is already a member of the group
        $checkStmt = $pdo->prepare("SELECT * FROM group_members WHERE group_id = ? AND user_id = ?");
        $checkStmt->execute([$group_id, $user_id]);

        if ($checkStmt->rowCount() > 0) {
            echo "<script>alert('You are already a member of this group.'); window.location.href = 'group_dashboard.php';</script>";
        } else {
            // Add the user to the group
            $insertStmt = $pdo->prepare("INSERT INTO group_members (group_id, user_id) VALUES (?, ?)");
            if ($insertStmt->execute([$group_id, $user_id])) {
                echo "<script>alert('Successfully joined the group!'); window.location.href = 'group_dashboard.php';</script>";
            } else {
                echo "<script>alert('Failed to join the group. Please try again.'); window.location.href = 'group_dashboard.php';</script>";
            }
        }
    } else {
        // Debugging: Log the invalid join_link
        error_log("Invalid join_link: " . $join_link);
        echo "<script>alert('Invalid join link. Please try again.'); window.location.href = 'group_dashboard.php';</script>";
    }
} else {
    header("Location: group_dashboard.php");
    exit();
}
?>